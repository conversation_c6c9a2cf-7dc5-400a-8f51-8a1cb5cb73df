#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
构建脚本：将iLook测试日志抓取工具打包成exe文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def create_adb_folder():
    """创建ADB文件夹（如果不存在）"""
    adb_path = Path("ADB")
    if not adb_path.exists():
        adb_path.mkdir()
        print("已创建ADB文件夹")
        
        # 创建一个说明文件
        readme_path = adb_path / "README.txt"
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write("请将adb.exe文件放置在此文件夹中\n")
            f.write("ADB工具可以从Android SDK或其他来源获取\n")
        print("已在ADB文件夹中创建说明文件")

def build_exe():
    """使用PyInstaller构建exe文件"""
    script_name = "ilook测试日志抓取工具.py"
    
    if not os.path.exists(script_name):
        print(f"错误：找不到脚本文件 {script_name}")
        return False
    
    # PyInstaller命令参数
    cmd = [
        "pyinstaller",
        "--onefile",  # 打包成单个exe文件
        "--windowed",  # 不显示控制台窗口（可选）
        "--name=iLook测试日志抓取工具",  # 指定exe文件名
        "--icon=NONE",  # 如果有图标文件可以指定
        "--add-data=ADB;ADB",  # 包含ADB文件夹
        "--hidden-import=pyodbc",  # 确保包含pyodbc模块
        "--hidden-import=colorama",  # 确保包含colorama模块
        script_name
    ]
    
    print("开始构建exe文件...")
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("构建成功！")
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"构建失败：{e}")
        print(f"错误输出：{e.stderr}")
        return False

def clean_build_files():
    """清理构建过程中产生的临时文件"""
    dirs_to_remove = ["build", "__pycache__"]
    files_to_remove = ["*.spec"]
    
    for dir_name in dirs_to_remove:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"已删除临时目录: {dir_name}")
    
    import glob
    for pattern in files_to_remove:
        for file_path in glob.glob(pattern):
            os.remove(file_path)
            print(f"已删除临时文件: {file_path}")

def main():
    """主函数"""
    print("=== iLook测试日志抓取工具 - EXE构建脚本 ===")
    
    # 创建ADB文件夹
    create_adb_folder()
    
    # 构建exe
    if build_exe():
        print("\n构建完成！")
        print("生成的exe文件位于 dist 文件夹中")
        
        # 询问是否清理临时文件
        response = input("\n是否清理构建过程中的临时文件？(y/n): ")
        if response.lower() in ['y', 'yes', '是']:
            clean_build_files()
            print("临时文件已清理")
    else:
        print("\n构建失败，请检查错误信息")

if __name__ == "__main__":
    main()
