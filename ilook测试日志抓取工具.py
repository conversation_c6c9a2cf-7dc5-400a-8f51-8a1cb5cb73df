import os,sys
import time,re
import os.path
import pyodbc
import subprocess
import shutil
import colorama
from colorama import Fore,Style
colorama.init()

print("iLook测试log抓取工具,请开启ADB调试模式_V1.4")
adbpath = os.getcwd() + "\\ADB"
path = os.getcwd()
BSN_list = []
item_list = []
ilook_adb = ""
beidou_file = ""
post = ["维修","维修员","主板维修"]
sql_server = "DRIVER={SQL Server};SERVER=***********;BATABASE=BW_OA;UID=sa;PWD=*****;"

def Readbsn():  #读取单板条码
    if os.path.isfile("bsn.txt"):
        os.remove("bsn.txt")
    res = -1
    res = os.system("adb devices > bsn.txt")
    if res == 1:
        pass
    else:
        with open("bsn.txt") as f:
            attached = f.readline()
            for i in range(1):  #是否有多个手机连接
                BSN = f.readline()
                BSN = BSN.strip("\n")
                if BSN == "":
                    return False
                else:
                    BSN = BSN.split("\t")
                    f.close()
                    os.remove("bsn.txt")
                    BSN_list.append(BSN[0])
                    return True

def ReadVersion():  #读取软件版本
    global version
    if os.path.isfile("version.txt"):
        os.remove("version.txt")
    res = -1
    res = os.system("adb shell getprop ro.build.display.id > version.txt")
    if res == 1:
        print(Fore.RED + "手机信息读取失败,请确认是否开启ADB调试！" + Style.RESET_ALL)
        return False
    else:
        with open("version.txt") as f:
            version = f.readline()
            f.close()
            os.remove("version.txt")
            version = version.strip("\n")
            return True

def Get_log(beidou_file,bsn,model):  #检测读取测试Log
    global test_item,Test_Result,beidou_test,fail,pas,nonsupport,BoardSN
    fail = 0
    pas = 0
    nonsupport = 0
    print("正在获取测试log文件！")
    while True:
        #分层进入文件目录，防止目录缺失导致抛出异常
        os.chdir(beidou_file)
        file_list = os.listdir(beidou_file)
        time.sleep(1)
        if model not in file_list:
            print(Fore.RED + "机型{}还未进行测试！".format(Model[0]) + Style.RESET_ALL)
            continue
        else:
            path = os.path.join(beidou_file,model)
            os.chdir(path)
            Barcode = os.listdir(beidou_file + "\\" + model)
            if bsn not in Barcode:
                print(Fore.RED + "条码:{}还未进行测试！".format(bsn) + Style.RESET_ALL)
                continue
            else:
                #进入对应条码文件夹，获取Beidou测试log日志
                os.chdir(beidou_file + "\\" + model + "\\" + bsn + "\\")
                beidou_test = os.listdir(beidou_file + "\\" + model + "\\" + bsn + "\\")
                if len(beidou_test) >= 1:
                    # q = os.path.getctime(os.path.join(beidou_test[0]))
                    # d = time.gmtime(q)
                    # times = str(d[0]) + "-" + str(d[1]) + "-" + str(d[2])
                    # currtime = time.strftime("%Y-%m-%d",time.localtime(time.time()))
                    # if times[5:7] == currtime[5:7]:
                    print(Fore.GREEN + "Beidou测试Log获取成功，正在读取诊断项目......" + Style.RESET_ALL)
                    break
                else:
                    print(Fore.RED + "Beidou测试Log获取失败,请进行测试" + Style.RESET_ALL)
                    time.sleep(1)
                    continue
    #读取beidou_test_result文件内容
    time.sleep(2)
    with open("beidou_test_result.xml",encoding="utf-8") as f:
        test_result = f.readlines()
        f.close()
        for t in test_result:
            item = t.strip(" ")
            item = item.strip("\n")
            if model == "NDL-W09":
                if "snInfo" in item:
                    BoardSN = re.sub(r"[^\w\s]+","",item).strip(" ").split("value")[1]
                    continue
            else:
                if "BoardSN" in item:
                    BoardSN = re.sub(r"[^\w\s]+","",item).strip(" ").split("value")[1]
                    continue
            if "itemKeyName" in item:
                item = item.split(" ")
                itemname = item[1].split("=")[1]
                itemname = itemname.strip('"')
                try:
                    for status in item:
                        if "failTimes" in status:
                            test_status = status.split("=")[1]
                            test_status = test_status.strip('"')
                            break
                except:
                    pass
                #Log诊断项目状态，1为Fail、-1为不支持、0为PASS
                if test_status == "1":
                    print(Fore.RED + "诊断项目：{},诊断结果：失败".format(itemname) + Style.RESET_ALL)
                    item_list.append("诊断项目:{},诊断结果:失败".format(itemname))
                    fail += 1
                if test_status == "-1":
                    print(Fore.YELLOW + "诊断项目：{},诊断结果：不支持该项目".format(itemname) + Style.RESET_ALL)
                    item_list.append("诊断项目:{},诊断结果:不支持该项目".format(itemname))
                    nonsupport += 1
                if test_status == "0":
                    print(Fore.GREEN + "诊断项目：{},诊断结果：成功".format(itemname) + Style.RESET_ALL)
                    item_list.append("诊断项目:{},诊断结果:成功".format(itemname))
                    pas += 1
                test_status = ""
                itemname = ""
                test_item = ""
            else:
                continue
        
        #判断测试项目是否有是失败项，有失败项则为NG
        test_item = "|".join(item_list)
        if "失败" in test_item:
            Test_Result = "NG"
        else:
            Test_Result = "OK"
        return True

def Uploda_result(sql_server,BoardSN): #测试日志上传数据库
    try:
        connect = pyodbc.connect(sql_server)
    except:
        print(Fore.RED + "Server Connect Fail!" + Style.RESET_ALL)
        os.system("pause")
        sys.exit()
    try:
        if connect:
            cursor = connect.cursor()
            count = cursor.execute("insert into MES_Honor.dbo.iLook_log(VeneerCode,Record,Test_Result,CreateById,Createtime) values('{}','{}','{}','{}',getdate())".format(BoardSN,test_item,Test_Result,Usercode))
            if count.rowcount >= 1:
                connect.commit()
                connect.close()
                return True
            else:
                print(Fore.RED + "条码:{}诊断结果上传失败，请联系IT人员" + Style.RESET_ALL.format(BoardSN))
                return False
    except:
        print(Fore.RED + "Server Connect Fail!" + Style.RESET_ALL)
        os.system("pause")
        sys.exit()

def User_inqire(sql_server):  #工号判定是否为维修员，符合则跳出，不符合则死循环
    while True:
        global Usercode
        Usercode = input("请输入你的工号：")
        connect = pyodbc.connect(sql_server)
        if connect:
            cursor = connect.cursor()
            cursor.execute("SELECT UserName,UserCode,Post  FROM BW_OA.dbo. EmpInfo WHERE (EmpState = '试用' OR EmpState = '在职')  AND Department = '荣耀' AND Post LIKE '%维修%' and UserCode = '{}'".format(Usercode))
            if cursor.rowcount == 0:
                print(Fore.RED + "工号不存在！" + Style.RESET_ALL)
                continue
            else:
                user_info = cursor.fetchall()
                for user in user_info:
                    if user[2] in post:
                        os.system("cls")
                        print(Fore.GREEN + "当前工号符合权限要求！".format(Usercode) + Style.RESET_ALL)
                        connect.commit()
                        connect.close()
                        return True
                    else:
                        print(Fore.RED + "工号不符合使用权限" + Style.RESET_ALL)
                        connect.commit()
                        connect.close()
                        continue
        else:
            sys.exit()

def kill_adb(): #关闭adb后台
    result = subprocess.run("tasklist",capture_output=True,text=True)
    output = result.stdout.strip()
    if "adb.exe" in output:
        res = os.system("adb kill-server")
    else:
        return True

def phone_is_connect(): # 判断手机是否连接，未连接则关闭adb程序
    # ADB命令获取手机是否连接，成功返回0，失败返回1
    os.chdir(adbpath)
    while True:
        res = -1
        res = os.system("adb root")
        if res == 1:
            kill_adb()
            break
        else:
            print(Fore.RED + "检测到手机连接，请断开连接" + Style.RESET_ALL)
            time.sleep(1)
            continue

def SSD():  #寻找磁盘是否有ilook文件
    ssd = "C:\\"
    if not Ergodic_file(ssd):
        # print("{}盘不存在".format(ssd.strip(":")))
        ssd = "D:\\"
    else:
        return True
    if not Ergodic_file(ssd):
        # print("{}盘不存在".format(ssd.strip(":")))
        ssd = "E:\\"
    else:
        return True
    if not Ergodic_file(ssd):
        # print("{}盘不存在".format(ssd.strip(":")))
        ssd = "F:\\"
    else:
        return True
    if not Ergodic_file(ssd):
        # print("{}盘不存在".format(ssd.strip(":")))
        ssd = "G:\\"
    else:
        return True
    if not Ergodic_file(ssd):
        print(Fore.RED + "未检测到ilook平台目录，请确保安装在硬盘根目录！" + Style.RESET_ALL)
        os.system("pause")
        sys.exit()
    else:
        return True

def Ergodic_file(ssd):   #找到文件后删除
    global ilook_adb
    global beidou_file
    try:
        os.chdir(ssd)
    except:
        print(Fore.RED + "未检测到ilook平台目录，请确保安装在硬盘根目录！" + Style.RESET_ALL)
        os.system("pause")
        sys.exit()
    file_list = os.listdir()

    #由于安装路径发生变化，ADB的新路径为：盘符:\Honor\ILookClientNew\iLookClient_publish\ExternalPlugin\T241024160444208\Scripts\adbTools
    if "Honor" in file_list:
        phone_is_connect()
        os.chdir(ssd)
        ilook_path = "Honor\\ILookClientNew\\iLookClient_publish\\ExternalPlugin\\T241024160444208\\Scripts\\adbTools\\"
        os.chdir(ilook_path)
        if os.path.isfile("adb.exe"):
            try:
                os.remove("adb.exe")
            except:
                kill_adb()
                os.remove("adb.exe")
            print(Fore.GREEN + "文件已初始化" + Style.RESET_ALL)
        ilook_adb = ssd + ilook_path

    if "BeiDouLog" in file_list:
        beidou_path = "BeiDouLog\\DetectLog\\"
        beidou_file = ssd + beidou_path

    if ilook_adb and beidou_file != "":
        return True
    else:
        return False

if __name__ == "__main__":
    SSD()
    User_inqire(sql_server)

    while True:
        try:
            phone_is_connect()  #判断手机是否连接，未连接则关闭adb程序并删除iLook平台adb文件
            os.chdir(ilook_adb)
            if os.path.isfile("adb.exe"):
                os.remove("adb.exe")
                print(Fore.GREEN + "文件已初始化" + Style.RESET_ALL)
            os.chdir(adbpath)
        except:
            print(Fore.RED + "adb文件目录不存在，请检查！" + Style.RESET_ALL)
            os.system("timeout \t 3")
            sys.exit()
        if not Readbsn():   #读取单板条码
            print(Fore.RED + "条码读取失败,请检查手机是否连接！" + Style.RESET_ALL)
            continue
        else:
            os.system("cls")
            for bsn in BSN_list:
                print(Fore.GREEN + "***************************************\n读取条码{}成功！".format(bsn) + Style.RESET_ALL)
                if not ReadVersion():  # 读取软件版本
                    continue
                else:
                    if "-BD" in version:
                        Model = version.strip("\n").split("-BD")
                    elif "-CHN" in version:
                        Model = version.strip("\n").split("-CHN")
                    elif " " in version:
                        Model = version.strip("\n").split(" ")
                    print(Fore.GREEN + "手机信息读取成功,软件版本:{}，产品名称：{}\n".format(version, Model[0]) + Style.RESET_ALL)
                    shutil.copy(adbpath + "\\adb.exe", ilook_adb)  # 给ilook平台补充adb文件
                    beidou_file = "D:\\BeiDouLog\\DetectLog\\"

                # beidou_file = r"F:\My-Python项目\Honor_iLook\q"
                # bsn = "AYKP9X24A4G07305"
                # Model = "NDL-W09"
                if not Get_log(beidou_file,bsn, Model[0]):  # 读取log日志
                    time.sleep(1)
                else:
                    print("条码:{},共诊断{}个项目,失败{}项，成功{}项,不支持{}项\n***************************************".format(BoardSN, len(item_list),fail,pas,nonsupport))
                    BSN_list.clear()
                    item_list.clear()
                if not Uploda_result(sql_server, BoardSN):  # 上传测试结果
                    continue
                else:
                    print(Fore.GREEN + "条码:{}诊断结果上传成功！".format(BoardSN) + Style.RESET_ALL)
                    BoardSN = ""
            os.system("timeout \t 10000")